/* TV Show Details Page Styles */

/* Banner Section */
.details-banner {
    position: relative;
    background-size: cover;
    background-position: center;
    padding: 100px 0 50px;
    color: #fff;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%);
}

.details-content {
    position: relative;
    z-index: 1;
}

.poster-container {
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border-radius: 8px;
    overflow: hidden;
}

.details-poster {
    width: 100%;
    border-radius: 8px;
    display: block;
}

.premium-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.details-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.details-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.details-rating {
    color: #ffc107;
    font-weight: bold;
}

.details-description h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #f8f9fa;
}

.details-description p {
    color: #adb5bd;
    line-height: 1.6;
}

.details-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Section Header */
.section-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #343a40;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0;
}

.section-title i {
    color: #dc3545;
}

/* Episode List */
.episode-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.episode-item {
    background-color: rgba(33, 37, 41, 0.7);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.episode-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.episode-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: rgba(33, 37, 41, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.episode-number {
    background-color: #dc3545;
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
    margin-right: 15px;
    min-width: 60px;
    text-align: center;
}

.episode-title {
    flex-grow: 1;
}

.episode-title h5 {
    margin-bottom: 5px;
    font-size: 1.1rem;
    color: #fff;
}

.episode-meta {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #adb5bd;
}

.premium-tag {
    color: #ffc107;
    font-weight: bold;
}

.episode-content {
    display: flex;
    padding: 15px;
}

.episode-thumbnail {
    width: 180px;
    min-width: 180px;
    height: 100px;
    margin-right: 15px;
    border-radius: 5px;
    overflow: hidden;
}

.episode-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.episode-details {
    flex-grow: 1;
}

.episode-description {
    color: #adb5bd;
    font-size: 0.9rem;
    margin-bottom: 15px;
    line-height: 1.5;
}

.episode-actions {
    display: flex;
    gap: 10px;
}

.episode-downloads {
    background-color: rgba(33, 37, 41, 0.5);
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.downloads-header {
    font-size: 0.9rem;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10px;
}

.download-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.download-link {
    display: flex;
    align-items: center;
    background-color: rgba(13, 110, 253, 0.15);
    border: 1px solid rgba(13, 110, 253, 0.3);
    border-radius: 8px;
    padding: 10px 15px;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    flex-grow: 1;
    min-width: 200px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 5px;
}

.download-link:hover {
    background-color: rgba(13, 110, 253, 0.25);
    color: #fff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.premium-link {
    background-color: rgba(220, 53, 69, 0.15);
    border: 2px solid rgba(220, 53, 69, 0.5);
    background-image: linear-gradient(to right, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.2));
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.premium-link::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #dc3545, #e83e8c, #fd7e14, #ffc107, #e83e8c, #dc3545);
    background-size: 400% 400%;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.5;
    animation: premium-background 15s ease infinite;
}

@keyframes premium-background {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.premium-link:hover {
    background-color: rgba(220, 53, 69, 0.25);
    background-image: linear-gradient(to right, rgba(220, 53, 69, 0.15), rgba(220, 53, 69, 0.3));
    transform: translateY(-3px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.3);
}

/* Premium Download Container */
.premium-download-container {
    position: relative;
    margin-bottom: 15px;
}

.premium-banner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-color: #dc3545;
    color: white;
    text-align: center;
    padding: 5px 0;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
    z-index: 1;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
}

.premium-banner i {
    color: #ffc107;
    margin-right: 5px;
    animation: crown-pulse 1.5s infinite;
}

@keyframes crown-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.premium-download-container .download-link {
    margin-top: 30px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-color: #dc3545;
}

.premium-download-container .download-button {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.download-quality {
    background-color: #0d6efd;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.premium-quality {
    background-color: #dc3545;
    background-image: linear-gradient(to right, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4);
    border: 2px solid #ffc107;
    animation: premium-glow 2s infinite alternate;
}

@keyframes premium-glow {
    from {
        box-shadow: 0 0 5px #ffc107;
    }
    to {
        box-shadow: 0 0 15px #ffc107;
    }
}

.premium-quality i {
    margin-left: 5px;
    color: #ffc107;
    font-size: 0.7rem;
    vertical-align: 1px;
}

.download-server {
    color: #e9ecef;
    font-size: 0.85rem;
    margin-right: 12px;
    flex-grow: 1;
    font-weight: 500;
}

.premium-server {
    color: #ffc107;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.download-size {
    color: #adb5bd;
    font-size: 0.8rem;
    margin-right: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 4px 10px;
    border-radius: 4px;
    font-weight: 500;
}

.premium-size {
    background-color: rgba(220, 53, 69, 0.15);
    color: #ffc107;
    border: 1px solid rgba(220, 53, 69, 0.3);
    position: relative;
    font-weight: bold;
}

.premium-size::before {
    content: 'PREMIUM';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    background-color: #dc3545;
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: bold;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.download-button {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.download-link:hover .download-button {
    background-color: rgba(255, 255, 255, 0.25);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

/* Download link container for download and play buttons */
.download-link-container {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
}

.download-link-container .download-link {
    flex: 1;
    width: auto;
    border-radius: 5px 0 0 5px;
    margin: 0;
}

/* Play link styles */
.play-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #e50914;
    padding: 15px 10px;
    border-radius: 0 5px 5px 0;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    width: 60px;
}

.play-link:hover {
    background-color: #f40612;
    color: #fff;
}

.play-link-icon {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.play-link-text {
    font-size: 0.8rem;
    text-align: center;
}

/* Stream Container */
.stream-container, .download-container {
    padding: 20px;
    background-color: rgba(33, 37, 41, 0.5);
    border-radius: 8px;
}

.stream-header, .download-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #343a40;
    padding-bottom: 15px;
}

/* Download Button */
.download-button {
    display: flex;
    align-items: center;
    background-color: #007bff;
    color: white;
    padding: 12px 15px;
    border-radius: 5px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.download-button:hover {
    background-color: #0069d9;
    color: white;
    text-decoration: none;
}

.download-icon {
    background-color: rgba(255,255,255,0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.download-info {
    display: flex;
    flex-direction: column;
}

.download-text {
    font-weight: bold;
    font-size: 0.9rem;
}

.download-meta {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .details-title {
        font-size: 1.8rem;
    }

    .details-banner {
        padding: 80px 0 40px;
    }

    .details-meta {
        gap: 10px;
        font-size: 0.8rem;
    }

    .custom-tab-buttons {
        justify-content: center;
    }

    .episode-content {
        flex-direction: column;
    }

    .episode-thumbnail {
        width: 100%;
        height: 150px;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .download-links {
        flex-direction: column;
    }

    .download-link {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .details-title {
        font-size: 1.5rem;
    }

    .details-banner {
        padding: 60px 0 30px;
    }

    .tab-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .episode-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .episode-number {
        margin-bottom: 10px;
    }

    .episode-meta {
        flex-direction: column;
        gap: 5px;
    }

    .episode-actions {
        flex-direction: column;
    }
}
