<?php
// Include database configuration
require_once 'includes/config.php';

// Set header to XML
header("Content-Type: application/xml; charset=utf-8");

// Start XML file
echo '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">' . PHP_EOL;

// Function to add URL to sitemap
function addUrl($url, $lastmod, $changefreq, $priority, $image = null) {
    echo "\t<url>" . PHP_EOL;
    echo "\t\t<loc>" . htmlspecialchars($url) . "</loc>" . PHP_EOL;
    echo "\t\t<lastmod>" . $lastmod . "</lastmod>" . PHP_EOL;
    echo "\t\t<changefreq>" . $changefreq . "</changefreq>" . PHP_EOL;
    echo "\t\t<priority>" . $priority . "</priority>" . PHP_EOL;
    
    if ($image) {
        echo "\t\t<image:image>" . PHP_EOL;
        echo "\t\t\t<image:loc>" . htmlspecialchars($image['loc']) . "</image:loc>" . PHP_EOL;
        echo "\t\t\t<image:caption>" . htmlspecialchars($image['caption']) . "</image:caption>" . PHP_EOL;
        echo "\t\t</image:image>" . PHP_EOL;
    }
    
    echo "\t</url>" . PHP_EOL;
}

// Get site domain
$site_domain = SITE_URL;

// Add static pages
$static_pages = [
    ['url' => $site_domain . '/', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '1.0'],
    ['url' => $site_domain . '/movies.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.9'],
    ['url' => $site_domain . '/tvshows.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.9'],
    ['url' => $site_domain . '/categories.php', 'lastmod' => date('c'), 'changefreq' => 'weekly', 'priority' => '0.8'],
    ['url' => $site_domain . '/about.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.7'],
    ['url' => $site_domain . '/contact.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.7'],
    ['url' => $site_domain . '/privacy-policy.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/terms-of-service.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/blog.php', 'lastmod' => date('c'), 'changefreq' => 'weekly', 'priority' => '0.8'],
    ['url' => $site_domain . '/search.php', 'lastmod' => date('c'), 'changefreq' => 'daily', 'priority' => '0.6'],
    ['url' => $site_domain . '/register.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/login.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.5'],
    ['url' => $site_domain . '/faq.php', 'lastmod' => date('c'), 'changefreq' => 'monthly', 'priority' => '0.6'],
];

foreach ($static_pages as $page) {
    addUrl($page['url'], $page['lastmod'], $page['changefreq'], $page['priority']);
}

// Add movie review pages
$movie_query = "SELECT id, title, year, poster, updated_at FROM movies ORDER BY updated_at DESC LIMIT 1000";
$movie_result = mysqli_query($conn, $movie_query);

if ($movie_result && mysqli_num_rows($movie_result) > 0) {
    while ($movie = mysqli_fetch_assoc($movie_result)) {
        $movie_url = $site_domain . '/movie_details.php?id=' . $movie['id'];
        $lastmod = date('c', strtotime($movie['updated_at']));
        $image = [
            'loc' => $site_domain . '/uploads/' . $movie['poster'],
            'caption' => htmlspecialchars($movie['title'] . ' (' . $movie['year'] . ') - Review and Rating')
        ];
        
        addUrl($movie_url, $lastmod, 'monthly', '0.7', $image);
    }
}

// Add TV show review pages
$tvshow_query = "SELECT id, title, year, poster, updated_at FROM tv_shows ORDER BY updated_at DESC LIMIT 1000";
$tvshow_result = mysqli_query($conn, $tvshow_query);

if ($tvshow_result && mysqli_num_rows($tvshow_result) > 0) {
    while ($tvshow = mysqli_fetch_assoc($tvshow_result)) {
        $tvshow_url = $site_domain . '/tvshow_details.php?id=' . $tvshow['id'];
        $lastmod = date('c', strtotime($tvshow['updated_at']));
        $image = [
            'loc' => $site_domain . '/uploads/' . $tvshow['poster'],
            'caption' => htmlspecialchars($tvshow['title'] . ' (' . $tvshow['year'] . ') - Review and Rating')
        ];
        
        addUrl($tvshow_url, $lastmod, 'monthly', '0.7', $image);
    }
}

// Add category pages
$category_query = "SELECT id, name, updated_at FROM categories ORDER BY updated_at DESC";
$category_result = mysqli_query($conn, $category_query);

if ($category_result && mysqli_num_rows($category_result) > 0) {
    while ($category = mysqli_fetch_assoc($category_result)) {
        $category_url = $site_domain . '/category.php?id=' . $category['id'];
        $lastmod = date('c', strtotime($category['updated_at']));
        
        addUrl($category_url, $lastmod, 'weekly', '0.7');
    }
}

// Close XML file
echo '</urlset>';

// Save the generated sitemap to a file
$sitemap_content = ob_get_contents();
file_put_contents('sitemap.xml', $sitemap_content);
?>
